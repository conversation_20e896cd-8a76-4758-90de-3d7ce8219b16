<template>
  <div class="graph-content">
    <div class="left">
      <subjectSelect :selected="subject" @setSubject="setSubject" />
    </div>
    <div class="right" v-loading="loading">
      <div class="filter-box">
        <div class="filter-box-top">
          <div class="search-input">
            <el-input
              class="search-item"
              v-model="searchValue"
              placeholder="请输入关键词搜索"
              clearable
              :prefix-icon="Search"
              @keyup.enter="searchChange"
            />
            <el-button @click="searchChange">
              搜索
            </el-button>
          </div>
          <div class="my-paper-btn" @click="toMyPaper">
            <img src="@/assets/img/percision/write-blue.svg" alt=""> 我的试卷
          </div>
        </div>
        <el-form class="form-sty" :model="form" label-form="7.5rem">
          <el-form-item label="">
            <template v-slot:label>
              <div class="justify-box">教材</div>
              ：
            </template>
            <el-radio-group v-model="form.editionId" @change="queryReportueryReport">
              <el-radio :value="0">全部</el-radio>
              <el-radio
                v-for="item in versionSubjectArr"
                :value="item.editionId"
                :key="item.editionId"
                >{{ item.editionName }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="">
            <template v-slot:label>
              <div class="justify-box">试卷类型</div>
              ：
            </template>
            <el-radio-group v-model="form.source" @change="sourceChange">
              <el-radio :value="0">全部</el-radio>
              <el-radio v-for="item in sourceArr" :value="item.sourceId" :key="item.sourceId">{{
                item.sourceName
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" @change="searchChange">
            <template v-slot:label>
              <div class="justify-box">试卷年份</div>
              ：
            </template>
            <el-radio-group v-model="form.year">
              <el-radio v-for="item in yearArr" :value="item.value" :key="item.value">{{ item.name }}</el-radio>
              <el-radio value="more">更多</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" @change="searchChange">
            <template v-slot:label>
              <div class="justify-box">试卷地区</div>
              ：
            </template>
            <AreaSelect @getRegionId="getRegionId" />
          </el-form-item>
        </el-form>
      </div>
      <div class="test-box" v-loading="loading" v-show="isShow">
        <div class="test-wrap" v-for="item in testList" >
          <div class="test-box-item" @click="testDetail(item)">
            <div class="test-box-item-img">
              <span class="red-text" v-if="item.score&&item.score!='0'">{{item.score}}分</span>
            </div>
            <div class="test-box-item-info">
              <div class="test-box-item-info-title">
                {{item.title}}
              </div>
              <div class="test-box-item-info-data">
                <div>更新时间：{{item.reportDate}}</div>
                <div>浏览：{{ item.viewCount }}</div>
                <div>题量：{{ item.quesCount }}</div>
              </div>
            </div>
            <div class="test-box-item-btn">
              <div class="test-box-item-btn-it btn" @click.stop="handleDownload(item)">
                <img src="@/assets/img/percision/download.png" alt=""> 下载
              </div>
              <div class="test-box-item-btn-it blue-text" @click.stop="testDetail(item)">
                查看详情>
              </div>
            </div>
          </div>
          <div class="hui-line"></div>
        </div>
        <!-- 无数据 -->
        <div class="nodata" v-if="!testList.length">
          <img src="@/assets/img/note/nodata.png" />暂无试卷
        </div>
        <div class="pagination-box">
          <Pagination
              :total="pageData.total"
              :current="pageData.current"
              @currentSizeChange="currentSizeChange"
              @pageClick="pageClick"/>
        </div>
      </div>
    </div>
  </div>
  <!-- 下载试卷 -->
  <downloadTrestDialog v-if="dialogVisible" ref="downloadTrestDialogRef" :paper-detail="dowmloadData" />
  <!-- 购买会员弹窗 -->
  <buyVip :show="showVip"  @close="quitHide"></buyVip>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import subjectSelect from "@/views/components/subjectSelect/index.vue"
import AreaSelect from "@/views/components/areaSelect/index.vue"
import { getBookVersionApi } from "@/api/book"
import { getReportQueryApi } from "@/api/report"
import { computed, nextTick, onMounted, reactive, ref } from 'vue'
import downloadTrestDialog from "@/components/TruePaperDownload/index.vue"
import { Search } from '@element-plus/icons-vue'
import buyVip from "@/views/components/buyVip/index.vue"
import { useUserStore } from "@/store/modules/user"
import { createTrainToAtlasApi, getDetailsApi, reportTrainApi } from '@/api/training';
import { dataEncrypt } from "@/utils/secret"
const userStore = useUserStore()
import { storeToRefs } from 'pinia'
const newTrainingId =ref()
const { subjectObj, learnNow } = storeToRefs(userStore)
const router = useRouter()
const searchValue = ref("")
const isShow = ref(false)
const subject = computed(() => {
  return subjectObj.value.subject
})
const sourceArr = ref([
  {
    sourceName: "单元试卷",
    sourceId: 1
  },
  {
    sourceName: "月考试卷",
    sourceId: 2
  },
  {
    sourceName: "期中试卷",
    sourceId: 3
  },
  {
    sourceName: "期末试卷",
    sourceId: 4
  },
  {
    sourceName: "小升初真题",
    sourceId: 5
  },
  {
    sourceName: "小升初模拟",
    sourceId: 6
  }
])
const showVip = ref(false)

const yearArr = ref([
  {
    name: "全部",
    value: "0"
  }
])
const testList = ref([] as any[])
const versionSubjectArr = ref([] as IVersionInfo[])
const loading = ref(false)
const pageData = reactive({
  total: 0,
  current: 1,
  size: 10
})
let gradeId = learnNow.value.gradeId //年级id
let termId = learnNow.value.termId //学期id
let academic = learnNow.value.academic //学制
class IForm {
  regionId = 0
  editionId = 0
  source = 0
  year = "0"
}
const form = ref(new IForm())
const downloadTrestDialogRef = ref()
const dialogVisible = ref(false)
const dowmloadData = reactive({
    id: '',
    title: ''
})
onMounted(async() => {
  const currentYear = new Date().getFullYear()
  yearArr.value.push({ name: currentYear.toString(), value: currentYear.toString() })
  let countYear = currentYear
  for (let i = 0; i < 4; i++) {
    countYear -= 1
    yearArr.value.push({ name: countYear.toString(), value: countYear.toString() })
  }
  await getVersionByGrade()
  await queryReportueryReport()
})
const quitHide = async () => {
    showVip.value = false
    if (useUserStore().memberInfo) {
      await getVersionByGrade()
      await queryReportueryReport()
    }
  }
const setSubject = async (data: any) => {
  pageData.current = 1
  await getVersionByGrade()
  await queryReportueryReport()
}
// 判断会员状态
const getIsMember = () => {
    const isVip = useUserStore().memberInfo
    if (isVip) {
      return true
    } else {
      showVip.value = true
      return false
    }
  }
const toMyPaper = () => {
  router.push({
    path: '/true_paper/my_paperT',
    query: {
      data: dataEncrypt({
        pageSource: '1'
      }),
    }
  })
}
        // formdata.append("reportId", queryData.id)
        // formdata.append("noteSource", '3')
        // api = createTrainToAtlasApi
const getTrainingTd = (data: any) => {
  return new Promise((resolve, reject) => {
    let formdata = new FormData()
    formdata.append("isCreate", '0')
    formdata.append("reportId", data.id)
    formdata.append("source", data.source)
    reportTrainApi(formdata).then((res: any) => {
        if (res.data) {
          resolve(res.data)
            //试卷分析
          }
        }).catch((error) => {
          reject()
        })
  })
}
const testDetail = async(data: any) => {

  let trainingId = await getTrainingTd(data)

  if (getIsMember()) {
        let url = '/ai_percision/knowledge_graph_detail/true_paper_detail'
        if (data.status == 1) {
          //试卷分析
          url = '/true_paper/my_paperT/paper_analysisTM'

        } else {
          //查看试卷
          url = '/true_paper/my_paperT/true_paper_detailM'

        }
          // reportId: data.reportId,
          // trainingId: trainingId,
          // pageSource: queryData.pageSource
          router.push({
            path: url,
            query: {
              data: dataEncrypt({
                reportId: data.id,
                pageSource: '1',
                trainingId:trainingId
              }),
            }
          })
  }
}
const handleDownload = ({ id, title }: any) => {
  if (getIsMember()) {
      Object.assign(dowmloadData, {
      id: id,
      title: title
    })
    dialogVisible.value = true
    nextTick(() => {
      downloadTrestDialogRef.value.dialogShow()
    })
  }
}
const searchChange = () => {
  pageData.current = 1
  queryReportueryReport()
}
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  queryReportueryReport()
}
const pageClick = (val: number) => {
  pageData.current = val
  queryReportueryReport()
}
//获取试题列表数据
const queryReportueryReport = async function () {
  const params = JSON.parse(JSON.stringify(form.value))
  params.subject = subjectObj.value.subject
  params.keyword = searchValue.value
  params.term = termId
  params.typeId = 0
  params.order = 3//0：试卷名称；1：浏览次数；2：下载次数；3：上载日期
  params.desc = 1//0：升序；1：降序
  params.grade = gradeId
  params.academic = academic
  params.current = pageData.current
  params.size = pageData.size
  if (params.year == "more") {
    const yearData = [] as string[]
    let lastYear = Number(yearArr.value[yearArr.value.length - 1]?.value)
    for (let i = 0; i < 1; i++) {
      lastYear -= 1
      yearData.push(lastYear.toString())
    }
    params.year = yearData.toString()
  }
  loading.value = true
  getReportQueryApi(params)
    .then((res: any) => {
      testList.value = res.data ? res.data.records : []
      pageData.total = Number(res.data?.total||0)
      loading.value = false
      isShow.value = true
    })
    .catch(() => {
      loading.value = false
      isShow.value = true
    })
}
//类型选择
const sourceChange = (value: any) => {
  pageData.current = 1
  queryReportueryReport()
}
//获取选中地区数据
const getRegionId = (id: number) => {
  form.value.regionId = id
  pageData.current = 1
  queryReportueryReport()
}
// 根据年级查教材版本
const getVersionByGrade = async () => {
  loading.value = true
  try {
    const res: any = await getBookVersionApi({
      gradeId,
      academic,
      termId
    })
    if (res.msg == "success") {
      res.data.map((item: any) => {
        if (item.subjectName == subjectObj.value.subjectName) {
          versionSubjectArr.value = item.versions
        }
      })
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.log(error)
  }
}
</script>
<style scoped lang="scss">
.graph-content {
  display: flex;
}
.right {
  margin-left: .625rem;
  width: 71.75rem;
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-rotute-tab-height) - .9375rem);
  overflow-y: auto;
  .filter-box {
    width: 71.375rem;
    background: #ffffff;
    box-sizing: border-box;
    border-radius: 1.25rem 1.25rem 0rem 0rem;
    &-top{
      display: flex;
      justify-content: space-between;
      padding: 1.875rem 1.25rem 1.25rem 1.25rem;
      border-bottom: .0625rem solid #eaeaea;
      .my-paper-btn {
        width: 6.875rem;
        height: 2.25rem;
        border-radius: .25rem;
        border: .0625rem solid #009C7F;
        color: #009C7F;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1.5625rem;
        font-size: 1rem;
        font-weight: 700;
        cursor: pointer;
        img {
          width: 1rem;
          height: 1rem;
          margin-right: .25rem;
        }
      }
      .search-input {
        width: 31.25rem;
        border-radius: 1.125rem;
        background: rgb(245, 245, 245);
        display: flex;
        align-items: center;
        justify-content: space-between;
        .search-item {
          width: 24.375rem;
          display: inline-block;
          :deep(.el-input.is-focus .el-input__wrapper) {
            box-shadow: none !important;
          }
          :deep(.el-input__wrapper.is-focus) {
            box-shadow: none !important;
          }
          :deep(.el-input__wrapper) {
            box-shadow: none;
            margin-left: .625rem;
            background: rgb(245, 245, 245);
            border-radius: .375rem;
            &:hover {
              box-shadow: none;
            }
          }
          :deep(.el-input-group__append) {
            background-color: var(--percision-tecah-main-color);
          }
          :deep(.el-input__inner) {
            width: 22.0625rem;
          }
        }
        :deep(.el-button) {
          border: none;
          height: 1.875rem;
          width: 5rem;
          border-radius: 1.125rem;
          margin-right: .3125rem;
          background-color: var(--percision-tecah-main-color);
          color: #ffffff;
        }
      }
    }
    .form-sty {
      padding: 1.25rem 1.25rem 0;
    }
    .justify-box {
      width: 3.75rem;
      text-align-last: justify;
      text-align: justify;
    }
    :deep(.el-radio) {
      margin-right: 1.5rem;
    }
    :deep(.el-radio__input) {
      display: none;
    }
    :deep(.el-radio__label) {
      text-align: center;
      padding: .1875rem .625rem;
      min-width: 3.75rem;
      color: #4d4d4d;
      line-height: 1.5rem;
      font-weight: bold;
    }
    .gery {
      :deep(.el-radio__label) {
        color: #cccccc;
      }
    }
    :deep(.el-radio.is-checked .el-radio__label) {
      border-radius: .25rem;
      width: 100%;
      background: #e3fff7;
      color: var(--percision-tecah-select-font-color) !important;
      font-weight: bold;
    }
    :deep(.el-form-item__label) {
      color: #7f7f7f;
      font-size: .875rem;
    }
  }
}
.test-box {
  width: 71.375rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  background: #ffffff;
  margin:-0.625rem 0 0;
  .test-wrap{
    width: 100%;
    .hui-line{
      width: 69.25rem;
      border-bottom: .0625rem dashed #EAEAEA;
      margin: 0 0 0 .875rem;
      float: left;
    }
  }
  .test-wrap:last-child{
    padding-bottom: 1.25rem;
  }
  &-item {
    width: 100%;
    height: 6.875rem;
    display: flex;
    padding: 1.25rem 1.25rem 1.25rem 1.25rem;
    box-sizing: border-box;
    &:hover {
      background: #effdfb;
    }
    &-img {
      width: 3.1875rem;
      height: 100%;
      font-size: .75rem;
      background-image: url(@/assets/img/percision/test-img.png);
      background-size: 100%;
      background-repeat: no-repeat;
      text-align: center;
      span {
        display: inline-block;
        margin-top: 1.85rem;
      }
    }
    &-info {
      margin-left: 1rem;
      width: 57rem;
      margin-right: 1.25rem;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      &-title {
        color: #2a2b2a;
        font-size: 1rem;
        font-weight: 400;
      }
      &-data {
        div {
          height: 1.75rem;
          border-radius: .875rem;
          background: #fef8e9;
          color: #ef9d19;
          display: inline-block;
          box-sizing: border-box;
          padding: .375rem .75rem;
          font-size: .75rem;
          margin-right: .625rem;
        }
      }
    }
    &-btn {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      &-it {
        width: 5.25rem;
        height: 1.875rem;
        line-height: 1.875rem;
        border-radius: .25rem;
        font-size: .875rem;
        text-align: center;
        cursor: pointer;
        img {
          width: .875rem;
          height: .875rem;
        }
      }
    }
  }
}
.btn {
  color: #ffffff;
  background: #00c9a3;
}
.grey-btn {
  background: #f5f5f5;
  color: #999999;
}
.red-text {
  color: #dd2a2a;
}
.blue-text {
  color: #009c7f;
}
.pagination-box {
  background-color: #fff;
  margin-top: 1.25rem;
  padding-bottom: 1.25rem;
}
/* 暂无数据 */
.nodata {
  flex: 1;
  color: #999999;
  font-size: .875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-flow: column;
  background: #fff;
  padding: 0 0 3.125rem;
}

.nodata img {
  width: 7.4375rem;
  height: 8rem;
  margin: 0 0 .625rem;
}
</style>

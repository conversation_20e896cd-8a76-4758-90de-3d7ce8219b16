<template>
  <div class="subject-container" :style="sty">
    <div class="tree-box">
      <el-tree
        :data="options"
        :props="iswen?defaultPropsWen:defaultProps"
        class="custom-tree"
        :node-key="iswen?'id':'chapterId'"
        ref="treeRef"
        @node-click="setChapterId"
        :default-expand-all = "true"
      >

        <template #default="{ node, data }">
          <!-- {{ data }} -->
          <div v-if="!node.isLeaf" class="custom-node" :class="node.expanded?'border-left':''">
            <!-- <el-tooltip
              class="box-item"
              effect="dark"
              :content="node.label"
              :hide-after="0"
              placement="top"
            > -->
              <span class="tree-h1" >{{ node.label }}</span>

            <!-- </el-tooltip> -->
            <el-icon class="expand-icon">
                <ArrowDown v-if="node.expanded"></ArrowDown>
                <ArrowUp v-else></ArrowUp>
            </el-icon>
          </div>
          <div v-else class="custom-node isLeaf">
            <!-- <el-tooltip
              class="box-item"
              effect="dark"
              :content="node.label"
              :hide-after="0"
              placement="top"
            > -->
              <span :class="{'is-current':isCurrent(node)}">{{ node.label }}</span>
            <!-- </el-tooltip> -->
              <div v-if="data.task" class="task-indicator">
                <!-- <img :src="getCurrentTaskImage()" class="task-badge" /> -->
                <img src="@/assets/img/synchronous/task-badge.png" class="task-badge" />
              </div>
              <div v-if="isStatus" class="btn" :class="data.status == 1 ? 'green-bg' : data.status == 2 ? 'yellow-bg' : data.status == 3 ? 'red-bg' : 'gray-bg'"
              > {{ data.status == 1 ? '已掌握' : data.status == 2 ? '一般' : data.status == 3 ? '未掌握' : (data.correctRate ? data.correctRate + '%' : '未测') }} </div>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, ref, watch, computed } from 'vue'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'

// 定义树节点数据接口
interface TreeNodeData {
  id?: string
  chapterId?: string
  name?: string
  chapterName?: string
  status?: number
  correctRate?: number
  children?: TreeNodeData[]
  [key: string]: any
}

const props = defineProps({
  options: {
    type: Array as () => TreeNodeData[],
    default: () => [] as TreeNodeData[]
  },
  selected: {
    type: String,
    default: () => ""
  },
  selectName: {
    type: String,
    default: () => ""
  },
  sty: {
    type: String,
    default: () => ""
  },
  iswen: {
    type: Boolean,
    default: () => false
  },
  isStatus: {
    type: Boolean,
    default: () => true
  },
  isHistoryTask: {
    type: Boolean,
    default: () => false
  },
  showCurrentTaskIndicator: {
    type: Boolean,
    default: () => false
  },
  currentTaskImageUrl: {
    type: String,
    default: () => "@/assets/img/percision/current-task.png"
  }
})

// 判断是否显示任务图标
const showTaskBadge = computed(() => {
  return props.isHistoryTask
})

const isCurrent = (data: any) => {
  console.log('isCurrent', props.selectName, props.selected)
  if (props.selectName) {
    return props.selected === data.key && props.selectName === data.label
  } else {
    return props.selected === data.key
  }

}

// 判断是否是当前选中的节点
const isCurrentNode = (data: TreeNodeData) => {
  const nodeKey = props.iswen ? data.id : data.chapterId
  return nodeKey === props.selected
}

const treeRef = ref()
watch(() => props.selected, (newValue) => {
  if(newValue) {
    nextTick(() => {
      treeRef.value.setCurrentKey(newValue)
      const targetData = props.iswen ? {id: newValue} : {chapterId: newValue}
      const fullName = getFullName(props.options, targetData)
      emit('setChapterName', fullName)
      // 自动滚动到选中节点
      scrollToSelected()
    })
  }
},{ immediate: true })
const emit = defineEmits(['setChapterId','setChapterName'])
const defaultProps = {
	value: 'chapterId',
  label: 'chapterName',
  children: 'children'
}
const defaultPropsWen = {
	value: 'id',
  label: 'name',
  children: 'children'
}

// 滚动到选中的节点
const scrollToSelected = () => {
  nextTick(() => {
    // 查找当前选中的节点元素
    const selectedNode = document.querySelector('.el-tree-node.is-current')
    if (selectedNode) {
      // 获取树容器
      const treeContainer = document.querySelector('.tree-box')
      if (treeContainer) {
        // 计算滚动位置，使选中节点居中显示
        const containerRect = treeContainer.getBoundingClientRect()
        const nodeRect = selectedNode.getBoundingClientRect()

        // 计算节点相对于容器的位置
        const relativeTop = nodeRect.top - containerRect.top

        // 计算滚动位置，使节点居中显示
        const scrollPosition = relativeTop + treeContainer.scrollTop - (containerRect.height / 2) + (nodeRect.height / 2)

        // 平滑滚动到计算出的位置
        treeContainer.scrollTo({
          top: Math.max(0, scrollPosition),
          behavior: 'smooth'
        })
      }
    }
  })
}

// 暴露方法给父组件使用
defineExpose({
  scrollToSelected
})

const setChapterId = (data: TreeNodeData) => {
  console.log(data,"data",props.options)
  if (!data.children || data.children.length == 0) {
    const fullName = getFullName(props.options, data)
    const nodeKey = props.iswen ? data.id : data.chapterId
    treeRef.value.setCurrentKey(nodeKey)
    emit('setChapterId', data, fullName)

    // 滚动到选中节点
    nextTick(() => {
      scrollToSelected()
    })
  }
}
const getFullName = (options: TreeNodeData[], data: TreeNodeData) => {
  let arr: TreeNodeData[] = []
  const getName = (node: TreeNodeData[], target: TreeNodeData, currentArr: TreeNodeData[]) => {
    node.map((item: TreeNodeData) => {
      const itemKey = props.iswen ? item.id : item.chapterId
      const targetKey = props.iswen ? target.id : target.chapterId

      if(itemKey == targetKey) {
        arr = currentArr.slice()
      } else if (item.children && item.children.length > 0) {
        getName(item.children, target, currentArr.concat(item))
      }
    })
  }

  getName(options, data, [])
  let fullName = ""
  arr.map((item: any) => {
    const itemName = props.iswen ? item.name : item.chapterName
    fullName += (itemName + "/")
  })

  const currentName = props.iswen ? data.name : data.chapterName
  return fullName + (currentName || '')
}
</script>
<style scoped lang="scss">
.is-current{
  color: #009c7f!important;
}
.tree-box {
    padding: .875rem;
    height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 6.25rem);
    overflow-y: auto;
}
.custom-tree {
  color: #666666;
  font-size: 1rem;
  .title-h1 {
    color: #2a2b2a;
    background: #f5f5f5;
  }
  .border-left {
    border-left: .1875rem solid #00C9A3;
  }
  /* 隐藏默认图标 */
  :deep(.el-tree-node__expand-icon) {
    display: none;
  }
  :deep(.el-tree-node) {
    width: 100%;
    margin: .5rem 0;  /* 增加节点间距 */
    position: relative;

    & > .el-tree-node__content {
      height: 2.5625rem;
      line-height: 2.5625rem;
    }
    .btn {
      width: 4.625rem;
      height: 1.4375rem;
      line-height: 1.4375rem;
      text-align: center;
      color: #ffffff;
      font-size: .75rem;
      border-radius: 1.125rem;
    }
  }
  /* 自定义节点布局 */
  .custom-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 .4375rem!important;
    box-sizing: border-box;
    span {
      display: inline-block;
      width: calc(100% - 5.3125rem);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    // height: 2.5625rem;
    // line-height: 2.5625rem;
    // margin-bottom: 1.125rem;
  }

  /* 右侧展开图标 */
  .expand-icon {
    margin-left: .5rem;
    font-size: .875rem;
    color: #666;
  }
}
:deep(.el-tree-node__children) {
  :not(.is-expanded) {
    .is-current {
      .el-tree-node__content {
        background: #e5f9f6!important;
      }
    }
  }
}
.subject-container {
  width: 23rem;
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  border-radius: 1.25rem 1.25rem 0 0;
  background: #ffffff;
  overflow-y: auto;
  padding-top: 3.75rem;
  position: relative;
  box-sizing: border-box;
}
.green-bg {
  background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
}
.yellow-bg {
  background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
}
.red-bg {
  background: linear-gradient(150.8deg, #f07f4c 0%, #c95656 100%);
}
.gray-bg {
  background: #bbbbbb;
}
.task-indicator {
  position: absolute;
  top: -11px;
  left: 0;
  z-index: 2;
}

.task-badge {
  width: 34px;
  height: 22px;
}
</style>

import { request } from "@/utils/axios"
const SDKPORT = "/api/xiaoyeoo/sdkapi/book"

/** 获取正在学习的章节 */
export function getStudyChapterApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${SDKPORT}/getStudyChapter`,
    method: "GET",
    params
  })
}
/** 获取章节下知识点列表 */
export function getpointListApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${SDKPORT}/pointList`,
    method: "GET",
    params
  })
}
/** 根据教材id获取章节列表 */
export function getChapterListApi(params: Object) {
  return request({
    url: `${SDKPORT}/getChapterList`,
    method: "GET",
    params
  })
}
/** 根据教材id获取章节列表--文科 */
export function getBookChapterListApi(params: Object) {
  return request({
    url: `${SDKPORT}/getBookChapterList`,
    method: "GET",
    params
  })
}

/** 根据教材id获取章节列表--文科 */
export function getBookChapterListsApi(params: Object) {
  return request({
    url: `${SDKPORT}/getChapterList`,
    method: "GET",
    params
  })
}

/** 根据教材id与知识点id获取章节信息 */
export function getChapterByBookAndPointApi(params: Object) {
  return request({
    url: `${SDKPORT}/getChapterByBookAndPoint`,
    method: "GET",
    params
  })
}
/** 查询章节掌握程度 */
export function getMasteryApi(params: Object) {
  return request({
    url: `${SDKPORT}/getMastery`,
    method: "GET",
    params
  })
}
/** 查询教材版本 */
export function getBookVersionApi(params: Object) {
  return request({
    url: `${SDKPORT}/version`,
    method: "GET",
    params
  })
}
/** 获取当前章节学习的步骤状态(1.弱项检测，2.针对学习，3学后检测，4.阶段测试，5.错题消化) */
export function getStudyStepApi(params: Object) {
  return request({
    url: `${SDKPORT}/getStudyStep`,
    method: "GET",
    params
  })
}

/** 查询地区树 */
export function getAreaTreeApi() {
  return request<IApiResponseData<IArea[]>>({
    url: `/webapi/xiaoyeoo/web/region/getTree`,
    method: "get"
  })
}

/** 查询当前地区 */
export function getNowAreaTreeApi() {
  return request<IApiResponseData<IArea>>({
    url: `/webapi/xiaoyeoo/web/region/getCity`,
    method: "get"
  })
}
/** getBookChapter */
export function getBookChapterApi() {
  return request<IApiResponseData<IArea>>({
    url: `/webapi/xiaoyeoo/web/region/getBookChapter`,
    method: "get"
  })
}
/** 获取教材下章节列表(Chapter) */
// export function getBookChapterListApi(params: Object) {
//   return request<IApiResponseData<IVersionInfo[]>>({
//     url: `${SDKPORT}/getBookChapterList`,
//     method: "GET",
//     params
//   })
// }


/** 查询章节掌握程度 */
export function getChapterListYxpApi(data: any) {
  return request({
    url: `${SDKPORT}/getChapterList_Yxp`,
    method: "GET",
    params: data
  })
}


// export const getBookChapterApi = (data : any) => {
//   return request({
//     url: `${sdkapi}/book/getBookChapter`,
//     method: "GET",
//     params: data,
//     headers: { "Content-Type": "application/x-www-form-urlencoded" }
//   })
// }

// 获取教材版本列表_yxp
export const getEditionList_YxprApi = (data : any) => {
  return request({
    url: `${SDKPORT}/getEditionList_Yxp`,
    method: "GET",
    params: data
  })
}

// 获取教材列表_yxp
export const getBookList_YxpApi = (data : any) => {
  return request({
    url: `${SDKPORT}/getBookList_Yxp`,
    method: "GET",
    params: data
  })
}